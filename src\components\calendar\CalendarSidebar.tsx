import React, { useState } from 'react';
import { ChevronDownIcon } from '../../icons';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import Checkbox from '../form/input/Checkbox';
import { useServices } from '../../hooks/useServices';
import { Service } from '../../types';

interface CalendarSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  // Calendar configuration
  startTime: string;
  endTime: string;
  timeSlotInterval: number;
  onConfigChange: (config: {
    startTime: string;
    endTime: string;
    timeSlotInterval: number;
  }) => void;
  // Services filter
  selectedServices: number[];
  onServicesChange: (serviceIds: number[]) => void;
  onNewService: () => void;
  // Status filter
  selectedStatuses: string[];
  onStatusesChange: (statuses: string[]) => void;
}

const CalendarSidebar: React.FC<CalendarSidebarProps> = ({
  isCollapsed,
  onToggle,
  startTime,
  endTime,
  timeSlotInterval,
  onConfigChange,
  selectedServices,
  onServicesChange,
  onNewService,
  selectedStatuses,
  onStatusesChange,
}) => {
  const [configExpanded, setConfigExpanded] = useState(true);
  const [servicesExpanded, setServicesExpanded] = useState(true);
  const [statusExpanded, setStatusExpanded] = useState(true);

  const { data: services = [] } = useServices();

  const timeSlotOptions = [
    { value: 15, label: '15 minutes' },
    { value: 30, label: '30 minutes' },
    { value: 45, label: '45 minutes' },
    { value: 60, label: '1 hour' },
  ];

  const statusOptions = [
    { value: 'pending', label: 'Pending', color: 'bg-yellow-500' },
    { value: 'confirmed', label: 'Confirmed', color: 'bg-blue-500' },
    { value: 'completed', label: 'Completed', color: 'bg-green-500' },
    { value: 'cancelled', label: 'Cancelled', color: 'bg-red-500' },
  ];

  const handleConfigChange = (field: string, value: string | number) => {
    onConfigChange({
      startTime,
      endTime,
      timeSlotInterval,
      [field]: value,
    });
  };

  const handleServiceToggle = (serviceId: number) => {
    const newSelection = selectedServices.includes(serviceId)
      ? selectedServices.filter(id => id !== serviceId)
      : [...selectedServices, serviceId];
    onServicesChange(newSelection);
  };

  const handleStatusToggle = (status: string) => {
    const newSelection = selectedStatuses.includes(status)
      ? selectedStatuses.filter(s => s !== status)
      : [...selectedStatuses, status];
    onStatusesChange(newSelection);
  };

  if (isCollapsed) {
    return (
      <div className="w-12 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
        <button
          onClick={onToggle}
          className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          title="Expand sidebar"
        >
          <ChevronDownIcon className="w-5 h-5 text-gray-500 dark:text-gray-400 rotate-90" />
        </button>
      </div>
    );
  }

  return (
    <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
      {/* Sidebar Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Calendar
        </h2>
        <button
          onClick={onToggle}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          title="Collapse sidebar"
        >
          <ChevronDownIcon className="w-5 h-5 text-gray-500 dark:text-gray-400 -rotate-90" />
        </button>
      </div>

      {/* Sidebar Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Calendar Config Section */}
        <div>
          <button
            onClick={() => setConfigExpanded(!configExpanded)}
            className="w-full flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Calendar Config
            </span>
            <ChevronDownIcon
              className={`w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform ${
                configExpanded ? 'rotate-180' : ''
              }`}
            />
          </button>

          {configExpanded && (
            <div className="mt-3 space-y-4">
              <div>
                <Label htmlFor="startTime">Start Time</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={startTime}
                  onChange={(e) => handleConfigChange('startTime', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="endTime">End Time</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={endTime}
                  onChange={(e) => handleConfigChange('endTime', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="timeSlotInterval">Time Slot Interval</Label>
                <select
                  id="timeSlotInterval"
                  value={timeSlotInterval}
                  onChange={(e) => handleConfigChange('timeSlotInterval', parseInt(e.target.value))}
                  className="h-11 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
                >
                  {timeSlotOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>

        {/* Services Section */}
        <div>
          <button
            onClick={() => setServicesExpanded(!servicesExpanded)}
            className="w-full flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Services
            </span>
            <ChevronDownIcon
              className={`w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform ${
                servicesExpanded ? 'rotate-180' : ''
              }`}
            />
          </button>

          {servicesExpanded && (
            <div className="mt-3 space-y-3">
              <Button
                variant="outline"
                size="sm"
                onClick={onNewService}
                className="w-full text-brand-600 border-brand-300 hover:bg-brand-50 dark:text-brand-400 dark:border-brand-800 dark:hover:bg-brand-900/20"
              >
                + New Service
              </Button>

              {services.length > 0 && (
                <div className="flex items-center justify-between text-xs">
                  <button
                    onClick={() => onServicesChange(services.map(s => s.id))}
                    className="text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300"
                  >
                    Select All
                  </button>
                  <button
                    onClick={() => onServicesChange([])}
                    className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                  >
                    Clear All
                  </button>
                </div>
              )}

              <div className="space-y-2 max-h-48 overflow-y-auto">
                {services.map((service) => (
                  <div key={service.id} className="flex items-center space-x-2">
                    <Checkbox
                      checked={selectedServices.includes(service.id)}
                      onChange={(checked) => {
                        if (checked) {
                          handleServiceToggle(service.id);
                        } else {
                          handleServiceToggle(service.id);
                        }
                      }}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: service.color }}
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300 truncate">
                          {service.title}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Status Section */}
        <div>
          <button
            onClick={() => setStatusExpanded(!statusExpanded)}
            className="w-full flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Status
            </span>
            <ChevronDownIcon
              className={`w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform ${
                statusExpanded ? 'rotate-180' : ''
              }`}
            />
          </button>

          {statusExpanded && (
            <div className="mt-3 space-y-3">
              <div className="flex items-center justify-between text-xs">
                <button
                  onClick={() => onStatusesChange(statusOptions.map(s => s.value))}
                  className="text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300"
                >
                  Select All
                </button>
                <button
                  onClick={() => onStatusesChange([])}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                >
                  Clear All
                </button>
              </div>

              <div className="space-y-2">
                {statusOptions.map((status) => (
                <div key={status.value} className="flex items-center space-x-2">
                  <Checkbox
                    checked={selectedStatuses.includes(status.value)}
                    onChange={(checked) => {
                      if (checked) {
                        handleStatusToggle(status.value);
                      } else {
                        handleStatusToggle(status.value);
                      }
                    }}
                  />
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${status.color}`} />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {status.label}
                    </span>
                  </div>
                </div>
              ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CalendarSidebar;
